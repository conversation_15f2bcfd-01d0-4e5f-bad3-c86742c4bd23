import React from 'react';

const LoadingSpinner = ({ size = 'md', text = 'Generating your image...', className = '' }) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
  };

  return (
    <div className={`flex flex-col items-center justify-center space-y-4 ${className} animate-fade-in`}>
      {/* Animated spinner */}
      <div className="relative">
        <div className={`${sizeClasses[size]} border-4 border-gray-200 dark:border-gray-700 rounded-full animate-spin`}>
          <div className="absolute top-0 left-0 w-full h-full border-4 border-transparent border-t-blue-500 rounded-full animate-spin"></div>
        </div>

        {/* Pulsing dots */}
        <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
        </div>
      </div>

      {/* Loading text */}
      {text && (
        <div className="text-center animate-fade-in-up">
          <p className={`${textSizeClasses[size]} text-gray-600 dark:text-gray-300 font-medium animate-pulse-slow`}>
            {text}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 animate-fade-in">
            This may take a few moments...
          </p>
        </div>
      )}

      {/* Progress bar animation */}
      <div className="w-64 bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
        <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
      </div>
    </div>
  );
};

// Simple inline spinner for buttons
export const ButtonSpinner = ({ className = '' }) => (
  <div className={`inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ${className}`}></div>
);

// Overlay loading component
export const LoadingOverlay = ({ isVisible, text = 'Loading...' }) => {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-8 shadow-2xl">
        <LoadingSpinner size="lg" text={text} />
      </div>
    </div>
  );
};

export default LoadingSpinner;
