import React from 'react';
import ImageGenerator from '../components/ImageGenerator';
import SEO, { pageSEO } from '../components/SEO';

const Generate = () => {
  return (
    <div className="relative">
      {/* SEO */}
      <SEO
        title="AI Image Generator - Create Stunning Art from Text | Gen Free AI"
        description="Generate beautiful AI artwork from text descriptions. Free, unlimited, and high-quality image generation powered by advanced AI technology."
        keywords="AI image generator, text to image, AI art, free image generator, artificial intelligence, creative tools, digital art"
      />

      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 -z-10"></div>

      {/* Content container */}
      <div className="relative z-10 py-8 px-4 sm:px-6 lg:px-8">
        <ImageGenerator />
      </div>
    </div>
  );
};

export default Generate;
