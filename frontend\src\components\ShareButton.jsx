import React, { useState } from 'react';
import { Share2, Facebook, Twitter, Instagram, Link, Copy, Check } from 'lucide-react';

const ShareButton = ({ imageUrl, prompt, onShare, className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  const shareText = `Check out this amazing AI-generated image: "${prompt}"`;
  const shareUrl = imageUrl; // Share the actual image URL instead of the site URL

  const shareOptions = [
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'bg-blue-600 hover:bg-blue-700',
      action: () => {
        // For Facebook, we share the image URL directly
        const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareText)}`;
        window.open(url, '_blank', 'width=600,height=400');
      }
    },
    {
      name: 'Twitter',
      icon: Twitter,
      color: 'bg-sky-500 hover:bg-sky-600',
      action: () => {
        // For Twitter, include the image URL in the tweet
        const tweetText = `${shareText} ${shareUrl}`;
        const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(tweetText)}`;
        window.open(url, '_blank', 'width=600,height=400');
      }
    },
    {
      name: 'Copy Image URL',
      icon: copied ? Check : Copy,
      color: copied ? 'bg-green-600' : 'bg-gray-600 hover:bg-gray-700',
      action: async () => {
        try {
          // Copy the image URL directly for easier sharing
          await navigator.clipboard.writeText(shareUrl);
          setCopied(true);
          setTimeout(() => setCopied(false), 2000);
        } catch (err) {
          console.error('Failed to copy:', err);
        }
      }
    }
  ];

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        // Convert image to blob for sharing
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const file = new File([blob], 'ai-generated-image.JPG', { type: 'image/JPG' });

        await navigator.share({
          title: 'AI Generated Image',
          text: shareText,
          files: [file]
        });

        if (onShare) onShare('native');
      } catch (err) {
        console.error('Error sharing:', err);
        // Fallback to URL sharing if file sharing fails
        try {
          await navigator.share({
            title: 'AI Generated Image',
            text: shareText,
            url: shareUrl
          });
          if (onShare) onShare('native');
        } catch (urlErr) {
          console.error('URL sharing also failed:', urlErr);
          setIsOpen(true); // Show manual share options
        }
      }
    } else {
      setIsOpen(true); // Show manual share options
    }
  };

  return (
    <div className="relative">
      <button
        onClick={handleNativeShare}
        className={`p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg
                 transition-all duration-200 shadow-lg flex items-center gap-2 ${className}`}
        title="Share image"
      >
        <Share2 className="w-5 h-5" />
        <span className="hidden sm:inline text-sm font-medium">Share</span>
      </button>

      {/* Manual Share Options Dropdown */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />

          {/* Dropdown */}
          <div className="absolute top-full right-0 mt-2 w-48 bg-white dark:bg-gray-800
                        rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
            <div className="p-2">
              <div className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2 px-2">
                Share on social media
              </div>

              {shareOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <button
                    key={option.name}
                    onClick={() => {
                      option.action();
                      if (onShare) onShare(option.name.toLowerCase());
                      if (option.name !== 'Copy Image URL') {
                        setIsOpen(false);
                      }
                    }}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-md text-white text-sm font-medium
                              transition-colors duration-200 mb-1 ${option.color}`}
                  >
                    <Icon className="w-4 h-4" />
                    {option.name}
                    {option.name === 'Copy Image URL' && copied && (
                      <span className="ml-auto text-xs">Copied!</span>
                    )}
                  </button>
                );
              })}

              <div className="border-t border-gray-200 dark:border-gray-600 mt-2 pt-2">
                <button
                  onClick={() => setIsOpen(false)}
                  className="w-full px-3 py-2 text-sm text-gray-600 dark:text-gray-300
                           hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ShareButton;
