import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  BookOpen,
  Calendar,
  Clock,
  ArrowRight,
  Tag,
  TrendingUp,
  Sparkles,
  Eye,
  Heart,
  Share2,
  Search,
  Filter,
  Grid,
  List,
  Zap,
  Target,
  Rocket,
  Globe,
  Home,
  FileText,
  Wand2,
  Image as ImageIcon,
  Lightbulb,
  Star
} from 'lucide-react';
import SEO from '../components/SEO';
import AdvertisementSpace from '../components/AdvertisementSpace';
import BlogPost from '../components/BlogPost';
import Breadcrumbs, { BreadcrumbStructuredData } from '../components/Breadcrumbs';

// Featured Post Card Component
const FeaturedPostCard = ({ post, onClick, index }) => (
  <div
    onClick={onClick}
    className="group relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 overflow-hidden cursor-pointer transform hover:scale-105"
  >
    <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/10 to-pink-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

    <div className="relative p-8">
      <div className="flex items-center gap-2 mb-4">
        <span className="px-3 py-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold rounded-full">
          FEATURED
        </span>
        <span className="text-sm text-gray-500 dark:text-gray-400">{post.readTime}</span>
      </div>

      <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
        {post.title}
      </h3>

      <p className="text-gray-600 dark:text-gray-300 mb-6 line-clamp-3">
        {post.excerpt}
      </p>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <Calendar className="w-4 h-4" />
            <span>{post.date}</span>
          </div>
          <div className="flex items-center gap-1">
            <Eye className="w-4 h-4" />
            <span>{post.views}</span>
          </div>
          <div className="flex items-center gap-1">
            <Heart className="w-4 h-4" />
            <span>{post.likes}</span>
          </div>
        </div>

        <button className="flex items-center gap-2 px-3 py-2 sm:px-4 sm:py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 group/btn text-sm sm:text-base">
          <span className="hidden sm:inline">Read More</span>
          <span className="sm:hidden">Read</span>
          <ArrowRight className="w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
        </button>
      </div>

      <div className="flex flex-wrap gap-2 mt-4">
        {post.tags.slice(0, 3).map((tag, tagIndex) => (
          <span
            key={tagIndex}
            className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded-full"
          >
            #{tag}
          </span>
        ))}
      </div>
    </div>
  </div>
);

// Regular Blog Post Card Component
const BlogPostCard = ({ post, onClick, viewMode, index }) => {
  if (viewMode === 'list') {
    return (
      <div
        onClick={onClick}
        className="group bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer border border-gray-200 dark:border-gray-700"
      >
        <div className="flex flex-col sm:flex-row">
          <div className="flex-1 p-6">
            <div className="flex items-center gap-2 mb-3">
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                post.category === 'tutorials' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
                post.category === 'comparisons' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :
                post.category === 'tools' ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' :
                'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
              }`}>
                {post.category}
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">{post.readTime}</span>
            </div>

            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              {post.title}
            </h3>

            <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
              {post.excerpt}
            </p>

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{post.date}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  <span>{post.views}</span>
                </div>
              </div>

              <button className="flex items-center gap-2 px-3 py-2 sm:px-4 sm:py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 text-xs sm:text-sm">
                <span className="hidden sm:inline">Read More</span>
                <span className="sm:hidden">Read</span>
                <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      onClick={onClick}
      className="group bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden cursor-pointer transform hover:scale-105 border border-gray-200 dark:border-gray-700"
    >
      <div className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
            post.category === 'tutorials' ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200' :
            post.category === 'comparisons' ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200' :
            post.category === 'tools' ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200' :
            'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
          }`}>
            {post.category}
          </span>
          <span className="text-sm text-gray-500 dark:text-gray-400">{post.readTime}</span>
        </div>

        <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-2">
          {post.title}
        </h3>

        <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 text-sm">
          {post.excerpt}
        </p>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              <span>{post.date}</span>
            </div>
            <div className="flex items-center gap-1">
              <Eye className="w-4 h-4" />
              <span>{post.views}</span>
            </div>
          </div>
        </div>

        <button className="w-full flex items-center justify-center gap-2 px-3 py-2 sm:px-4 sm:py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-lg transition-all duration-200 text-xs sm:text-sm">
          <span className="hidden sm:inline">Read More</span>
          <span className="sm:hidden">Read</span>
          <ArrowRight className="w-3 h-3 sm:w-4 sm:h-4" />
        </button>

        <div className="flex flex-wrap gap-1 mt-3">
          {post.tags.slice(0, 2).map((tag, tagIndex) => (
            <span
              key={tagIndex}
              className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded-full"
            >
              #{tag}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

const Blog = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedPost, setSelectedPost] = useState(null);
  const [viewMode, setViewMode] = useState('grid');
  const navigate = useNavigate();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const blogPosts = [
    {
      id: 1,
      title: "🎨 How to Write Perfect AI Art Prompts: Complete Guide for Beginners",
      excerpt: "Master the art of AI prompt writing! Learn proven techniques to create stunning AI-generated images from text. Transform your ideas into amazing visuals with our step-by-step guide.",
      content: `
        <div class="bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900 p-6 rounded-lg mb-6">
          <h2 class="text-2xl font-bold mb-4">🚀 Welcome to AI Art Mastery</h2>
          <p class="text-lg">Ready to create mind-blowing AI art? This comprehensive guide will teach you everything you need to know about writing effective prompts for AI image generation!</p>
        </div>

        <h3>🎯 What Makes a Great AI Prompt?</h3>
        <p class="mb-4">A great AI prompt is like a recipe - the more specific and detailed you are, the better your results will be. Think of AI as a very talented artist who needs clear instructions.</p>

        <div class="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-4 mb-6">
          <h4 class="font-bold text-blue-800 dark:text-blue-200 mb-2">✅ Good Prompt Example:</h4>
          <p class="italic">"A majestic golden retriever sitting in a sunlit meadow, professional photography, shallow depth of field, warm golden hour lighting, highly detailed fur texture"</p>
        </div>

        <div class="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 p-4 mb-6">
          <h4 class="font-bold text-red-800 dark:text-red-200 mb-2">❌ Poor Prompt Example:</h4>
          <p class="italic">"dog"</p>
        </div>

        <h3>🔑 Essential Prompt Components</h3>
        <div class="grid md:grid-cols-2 gap-4 mb-6">
          <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h4 class="font-bold text-green-800 dark:text-green-200 mb-2">📝 Subject Description</h4>
            <ul class="text-sm space-y-1">
              <li>• What is the main focus?</li>
              <li>• Physical characteristics</li>
              <li>• Pose or action</li>
              <li>• Facial expression</li>
            </ul>
          </div>
          <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <h4 class="font-bold text-purple-800 dark:text-purple-200 mb-2">🎨 Style & Quality</h4>
            <ul class="text-sm space-y-1">
              <li>• Art style (realistic, cartoon, etc.)</li>
              <li>• Quality keywords</li>
              <li>• Camera settings</li>
              <li>• Artistic medium</li>
            </ul>
          </div>
        </div>

        <h3>💡 Pro Tips for Better Results</h3>
        <div class="space-y-3 mb-6">
          <div class="flex items-start gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <span class="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5">💡</span>
            <span><strong>Be Specific:</strong> Instead of "beautiful," use "elegant," "stunning," or "breathtaking"</span>
          </div>
          <div class="flex items-start gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <span class="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5">💡</span>
            <span><strong>Use Quality Boosters:</strong> "highly detailed," "8K resolution," "professional photography"</span>
          </div>
          <div class="flex items-start gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <span class="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5">💡</span>
            <span><strong>Describe Lighting:</strong> "golden hour," "soft diffused light," "dramatic shadows"</span>
          </div>
        </div>

        <div class="bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900 dark:to-blue-900 p-6 rounded-lg">
          <h4 class="text-xl font-bold mb-3">🎯 Your First Assignment</h4>
          <p class="mb-3">Try this prompt and see the magic happen:</p>
          <p class="italic bg-white dark:bg-gray-800 p-3 rounded">"A cute corgi wearing sunglasses, sitting on a beach chair, tropical paradise background, professional photography, vibrant colors, highly detailed"</p>
        </div>
      `,
      category: "tutorials",
      readTime: "8 min read",
      date: "2025-01-15",
      featured: true,
      author: "GenFreeAI Team",
      tags: ["AI prompts", "beginner guide", "text to image", "tutorial"],
      views: 1250,
      likes: 89
    }
  ];

  // Add more blog posts
  const moreBlogPosts = [
    {
      id: 2,
      title: "🆚 GenFreeAI vs Midjourney vs DALL-E: Ultimate AI Art Comparison 2025",
      excerpt: "Discover which AI image generator reigns supreme! We compare GenFreeAI, Midjourney, and DALL-E across quality, cost, ease of use, and features. Find your perfect AI art tool!",
      content: `
        <div class="bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 p-6 rounded-lg mb-6">
          <h2 class="text-2xl font-bold mb-4">⚔️ The Ultimate AI Art Battle</h2>
          <p class="text-lg">Three titans enter, but which one will claim the crown? Let's dive deep into the most comprehensive comparison of today's top AI image generators!</p>
        </div>

        <h3>🏆 Quick Comparison Overview</h3>

        <!-- Mobile-friendly comparison cards (visible on small screens) -->
        <div class="block md:hidden space-y-4 mb-6">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 border-l-4 border-green-500">
            <h4 class="font-bold text-green-600 text-lg mb-3">GenFreeAI</h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between"><span>💰 Cost:</span><span class="font-bold text-green-600">FREE</span></div>
              <div class="flex justify-between"><span>🎨 Quality:</span><span>⭐⭐⭐⭐</span></div>
              <div class="flex justify-between"><span>⚡ Speed:</span><span>⭐⭐⭐⭐⭐</span></div>
              <div class="flex justify-between"><span>👥 Ease:</span><span>⭐⭐⭐⭐⭐</span></div>
            </div>
          </div>

          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 border-l-4 border-blue-500">
            <h4 class="font-bold text-blue-600 text-lg mb-3">Midjourney</h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between"><span>💰 Cost:</span><span>$10/month</span></div>
              <div class="flex justify-between"><span>🎨 Quality:</span><span>⭐⭐⭐⭐⭐</span></div>
              <div class="flex justify-between"><span>⚡ Speed:</span><span>⭐⭐⭐</span></div>
              <div class="flex justify-between"><span>👥 Ease:</span><span>⭐⭐⭐</span></div>
            </div>
          </div>

          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 border-l-4 border-purple-500">
            <h4 class="font-bold text-purple-600 text-lg mb-3">DALL-E</h4>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between"><span>💰 Cost:</span><span>$20/month</span></div>
              <div class="flex justify-between"><span>🎨 Quality:</span><span>⭐⭐⭐⭐</span></div>
              <div class="flex justify-between"><span>⚡ Speed:</span><span>⭐⭐⭐⭐</span></div>
              <div class="flex justify-between"><span>👥 Ease:</span><span>⭐⭐⭐⭐</span></div>
            </div>
          </div>
        </div>

        <!-- Desktop table (hidden on small screens) -->
        <div class="hidden md:block overflow-x-auto mb-6">
          <table class="w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg min-w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-3 py-3 lg:px-4 lg:py-3 text-left font-bold text-sm lg:text-base">Feature</th>
                <th class="px-3 py-3 lg:px-4 lg:py-3 text-center font-bold text-green-600 text-sm lg:text-base">GenFreeAI</th>
                <th class="px-3 py-3 lg:px-4 lg:py-3 text-center font-bold text-blue-600 text-sm lg:text-base">Midjourney</th>
                <th class="px-3 py-3 lg:px-4 lg:py-3 text-center font-bold text-purple-600 text-sm lg:text-base">DALL-E</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
              <tr>
                <td class="px-3 py-3 lg:px-4 lg:py-3 font-medium text-sm lg:text-base">💰 Cost</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-green-600 font-bold text-sm lg:text-base">FREE</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">$10/month</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">$20/month</td>
              </tr>
              <tr class="bg-gray-50 dark:bg-gray-700/50">
                <td class="px-3 py-3 lg:px-4 lg:py-3 font-medium text-sm lg:text-base">🎨 Quality</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">⭐⭐⭐⭐</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">⭐⭐⭐⭐⭐</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">⭐⭐⭐⭐</td>
              </tr>
              <tr>
                <td class="px-3 py-3 lg:px-4 lg:py-3 font-medium text-sm lg:text-base">⚡ Speed</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">⭐⭐⭐⭐⭐</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">⭐⭐⭐</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">⭐⭐⭐⭐</td>
              </tr>
              <tr class="bg-gray-50 dark:bg-gray-700/50">
                <td class="px-3 py-3 lg:px-4 lg:py-3 font-medium text-sm lg:text-base">👥 Ease of Use</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">⭐⭐⭐⭐⭐</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">⭐⭐⭐</td>
                <td class="px-3 py-3 lg:px-4 lg:py-3 text-center text-sm lg:text-base">⭐⭐⭐⭐</td>
              </tr>
            </tbody>
          </table>
        </div>

        <h3>🎯 Why Choose GenFreeAI?</h3>
        <div class="grid sm:grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 mb-6">
          <div class="bg-green-50 dark:bg-green-900/20 p-4 sm:p-6 rounded-lg border border-green-200 dark:border-green-700">
            <h4 class="font-bold text-green-800 dark:text-green-200 mb-3 text-base sm:text-lg">✅ Advantages</h4>
            <ul class="text-sm sm:text-base space-y-2">
              <li class="flex items-start gap-2">
                <span class="text-green-600 mt-1">•</span>
                <span>Completely FREE to use</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-green-600 mt-1">•</span>
                <span>No registration required</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-green-600 mt-1">•</span>
                <span>Lightning-fast generation</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-green-600 mt-1">•</span>
                <span>User-friendly interface</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-green-600 mt-1">•</span>
                <span>No daily limits</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-green-600 mt-1">•</span>
                <span>High-quality results</span>
              </li>
            </ul>
          </div>
          <div class="bg-blue-50 dark:bg-blue-900/20 p-4 sm:p-6 rounded-lg border border-blue-200 dark:border-blue-700">
            <h4 class="font-bold text-blue-800 dark:text-blue-200 mb-3 text-base sm:text-lg">🎨 Perfect For</h4>
            <ul class="text-sm sm:text-base space-y-2">
              <li class="flex items-start gap-2">
                <span class="text-blue-600 mt-1">•</span>
                <span>Beginners learning AI art</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-blue-600 mt-1">•</span>
                <span>Quick concept visualization</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-blue-600 mt-1">•</span>
                <span>Social media content</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-blue-600 mt-1">•</span>
                <span>Personal projects</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-blue-600 mt-1">•</span>
                <span>Experimenting with prompts</span>
              </li>
              <li class="flex items-start gap-2">
                <span class="text-blue-600 mt-1">•</span>
                <span>Budget-conscious creators</span>
              </li>
            </ul>
          </div>
        </div>

        <div class="bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 p-4 sm:p-6 rounded-lg text-center sm:text-left">
          <h4 class="text-lg sm:text-xl font-bold mb-3">🚀 Ready to Try GenFreeAI?</h4>
          <p class="mb-4 text-sm sm:text-base">Join thousands of creators who've discovered the power of free AI art generation. No credit card, no signup, no limits!</p>
          <a href="/" class="inline-flex items-center gap-2 px-4 py-2 sm:px-6 sm:py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors text-sm sm:text-base">
            <span class="w-4 h-4 sm:w-5 sm:h-5">🪄</span>
            <span class="hidden sm:inline">Start Creating Now</span>
            <span class="sm:hidden">Create Now</span>
          </a>
        </div>
      `,
      category: "comparisons",
      readTime: "6 min read",
      date: "2025-01-14",
      featured: true,
      author: "AI Expert",
      tags: ["comparison", "Midjourney", "DALL-E", "AI tools"],
      views: 2100,
      likes: 156
    },
    {
      id: 3,
      title: "🎯 10 Best Free AI Image Generators in 2025 (No Signup Required)",
      excerpt: "Discover the top free AI image generators that don't require registration! Create stunning AI art without spending a dime. Complete comparison with pros, cons, and examples.",
      content: `
        <div class="bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 p-6 rounded-lg mb-6">
          <h2 class="text-2xl font-bold mb-4">🆓 Free AI Art for Everyone</h2>
          <p class="text-lg">Who says you need to pay for amazing AI art? Here are the best free AI image generators that will blow your mind without emptying your wallet!</p>
        </div>

        <h3>🥇 Top Free AI Image Generators</h3>
        <div class="space-y-6 mb-8">
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center gap-3 mb-4">
              <span class="text-2xl">🏆</span>
              <h4 class="text-xl font-bold text-green-600">1. GenFreeAI</h4>
              <span class="px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs rounded-full">BEST OVERALL</span>
            </div>
            <p class="mb-3">The ultimate free AI image generator with no limits, no signup, and lightning-fast results.</p>
            <div class="grid md:grid-cols-2 gap-4">
              <div>
                <h5 class="font-semibold text-green-600 mb-2">✅ Pros:</h5>
                <ul class="text-sm space-y-1">
                  <li>• Completely free forever</li>
                  <li>• No registration needed</li>
                  <li>• Unlimited generations</li>
                  <li>• High-quality results</li>
                </ul>
              </div>
              <div>
                <h5 class="font-semibold text-red-600 mb-2">❌ Cons:</h5>
                <ul class="text-sm space-y-1">
                  <li>• Limited to 1024x1024 resolution</li>
                  <li>• No advanced editing features</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center gap-3 mb-4">
              <span class="text-2xl">🥈</span>
              <h4 class="text-xl font-bold text-blue-600">2. Craiyon (formerly DALL-E mini)</h4>
            </div>
            <p class="mb-3">The original free AI art generator that started it all. Simple, reliable, and always free.</p>
            <div class="grid md:grid-cols-2 gap-4">
              <div>
                <h5 class="font-semibold text-green-600 mb-2">✅ Pros:</h5>
                <ul class="text-sm space-y-1">
                  <li>• Completely free</li>
                  <li>• No account required</li>
                  <li>• Generates 9 images at once</li>
                  <li>• Simple interface</li>
                </ul>
              </div>
              <div>
                <h5 class="font-semibold text-red-600 mb-2">❌ Cons:</h5>
                <ul class="text-sm space-y-1">
                  <li>• Lower image quality</li>
                  <li>• Slower generation</li>
                  <li>• Limited resolution</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 p-6 rounded-lg">
          <h4 class="text-xl font-bold mb-3">🎨 Pro Tip for Free Users</h4>
          <p class="mb-3">To get the best results from free AI generators:</p>
          <ul class="space-y-2">
            <li class="flex items-start gap-2">
              <span class="text-blue-600">•</span>
              <span>Be very specific in your prompts</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="text-blue-600">•</span>
              <span>Use quality keywords like "highly detailed" and "professional"</span>
            </li>
            <li class="flex items-start gap-2">
              <span class="text-blue-600">•</span>
              <span>Try multiple variations of the same prompt</span>
            </li>
          </ul>
        </div>
      `,
      category: "tools",
      readTime: "7 min read",
      date: "2025-01-13",
      featured: false,
      author: "GenFreeAI Team",
      tags: ["free tools", "AI generators", "comparison", "no signup"],
      views: 1800,
      likes: 134
    }
  ];

  // Combine all blog posts
  const allBlogPosts = [...blogPosts, ...moreBlogPosts];

  const categories = [
    { id: 'all', name: 'All Posts', icon: Grid },
    { id: 'tutorials', name: 'Tutorials', icon: BookOpen },
    { id: 'comparisons', name: 'Comparisons', icon: Target },
    { id: 'tools', name: 'Tools', icon: Wand2 },
    { id: 'tips', name: 'Tips & Tricks', icon: Lightbulb },
    { id: 'news', name: 'AI News', icon: TrendingUp }
  ];

  const filteredPosts = allBlogPosts.filter(post =>
    selectedCategory === 'all' || post.category === selectedCategory
  );

  const featuredPosts = allBlogPosts.filter(post => post.featured);

  const handlePostClick = (post) => {
    setSelectedPost(post);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleBackToBlog = () => {
    setSelectedPost(null);
  };

  // If a post is selected, show the individual post view
  if (selectedPost) {
    return <BlogPost post={selectedPost} onBack={handleBackToBlog} />;
  }

  // Create breadcrumbs for blog
  const blogBreadcrumbs = [
    { label: 'Home', href: '/', icon: Home },
    { label: 'AI Art Blog', href: null, icon: BookOpen }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 py-8 relative overflow-hidden">
      {/* SEO */}
      <SEO
        title="🎨 Free AI Art Blog - Best Text to Image AI Tips & Tutorials | GenFreeAI"
        description="Master AI image generation with our expert guides! Learn prompt writing, compare AI tools like Midjourney vs DALL-E, and discover free alternatives. Best AI art tutorials 2025!"
        keywords="AI art blog, text to image AI, AI image generator tutorials, prompt writing guide, Midjourney alternative, DALL-E comparison, free AI art tools, AI art tips"
      />

      {/* Structured Data for Breadcrumbs */}
      <BreadcrumbStructuredData breadcrumbs={blogBreadcrumbs} />

      {/* Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-200 dark:bg-blue-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '0s' }}></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-purple-200 dark:bg-purple-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-40 left-20 w-20 h-20 bg-green-200 dark:bg-green-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '4s' }}></div>
        <div className="absolute bottom-20 right-10 w-28 h-28 bg-orange-200 dark:bg-orange-800 rounded-full opacity-10 animate-float" style={{ animationDelay: '6s' }}></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Breadcrumbs */}
        <Breadcrumbs customBreadcrumbs={blogBreadcrumbs} />

        {/* Enhanced Header */}
        <div className="text-center mb-16">
          <div className="relative inline-block mb-8 animate-bounce-in">
            <div className="w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-blue-100 via-purple-100 to-pink-100 dark:from-blue-900 dark:via-purple-900 dark:to-pink-900 rounded-3xl flex items-center justify-center shadow-lg">
              <BookOpen className="w-10 h-10 sm:w-12 sm:h-12 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="absolute -top-2 -right-2 animate-bounce">
              <Sparkles className="w-6 h-6 text-yellow-500" />
            </div>
          </div>

          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient-x mb-6">
            AI Art Mastery Blog
          </h1>

          <p className="text-xl sm:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
            Master the art of AI image generation with expert tutorials, comparisons, and insider tips
          </p>

          {/* Stats */}
          <div className="flex flex-wrap justify-center gap-6 mb-8">
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 rounded-full shadow-lg">
              <BookOpen className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <span className="font-semibold text-blue-700 dark:text-blue-300">{allBlogPosts.length} Expert Guides</span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900 dark:to-emerald-900 rounded-full shadow-lg">
              <Eye className="w-5 h-5 text-green-600 dark:text-green-400" />
              <span className="font-semibold text-green-700 dark:text-green-300">10K+ Readers</span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900 dark:to-pink-900 rounded-full shadow-lg">
              <Star className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              <span className="font-semibold text-purple-700 dark:text-purple-300">Free Forever</span>
            </div>
          </div>
        </div>

        {/* Categories Filter */}
        <div className="mb-12">
          <div className="flex flex-wrap justify-center gap-2 sm:gap-3 mb-8">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`
                    flex items-center gap-1 sm:gap-2 px-2 py-1 sm:px-4 sm:py-2 rounded-full font-medium transition-all duration-200 transform hover:scale-105 text-xs sm:text-sm
                    ${selectedCategory === category.id
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 shadow-md'
                    }
                  `}
                >
                  <Icon className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="hidden sm:inline">{category.name}</span>
                  <span className="sm:hidden">
                    {category.name === 'All Posts' ? 'All' :
                     category.name === 'Tutorials' ? 'Tuts' :
                     category.name === 'Comparisons' ? 'Comp' :
                     category.name === 'Tips & Tricks' ? 'Tips' :
                     category.name === 'AI News' ? 'News' :
                     category.name}
                  </span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Featured Posts Section */}
        {featuredPosts.length > 0 && (
          <div className="mb-16">
            <div className="flex items-center gap-3 mb-8">
              <Star className="w-6 h-6 text-yellow-500" />
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Featured Posts</h2>
            </div>

            <div className="grid lg:grid-cols-2 gap-8">
              {featuredPosts.slice(0, 2).map((post, index) => (
                <FeaturedPostCard
                  key={post.id}
                  post={post}
                  onClick={() => handlePostClick(post)}
                  index={index}
                />
              ))}
            </div>
          </div>
        )}

        {/* Advertisement Space */}
        <div className="mb-16">
          <AdvertisementSpace
            title="Support Free AI Art"
            description="Help us keep GenFreeAI free forever"
          />
        </div>

        {/* All Posts Section */}
        <div className="mb-16">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              {selectedCategory === 'all' ? 'All Posts' : categories.find(c => c.id === selectedCategory)?.name}
            </h2>

            <div className="flex items-center gap-1 sm:gap-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-1 sm:p-2 rounded-lg transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                }`}
              >
                <Grid className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-1 sm:p-2 rounded-lg transition-colors ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                }`}
              >
                <List className="w-4 h-4 sm:w-5 sm:h-5" />
              </button>
            </div>
          </div>

          <div className={`${viewMode === 'grid' ? 'grid md:grid-cols-2 lg:grid-cols-3 gap-8' : 'space-y-6'}`}>
            {filteredPosts.map((post, index) => (
              <BlogPostCard
                key={post.id}
                post={post}
                onClick={() => handlePostClick(post)}
                viewMode={viewMode}
                index={index}
              />
            ))}
          </div>

          {filteredPosts.length === 0 && (
            <div className="text-center py-16">
              <div className="relative">
                <div className="w-24 h-24 sm:w-32 sm:h-32 mx-auto bg-gradient-to-br from-gray-100 via-gray-200 to-gray-100 dark:from-gray-800 dark:via-gray-700 dark:to-gray-800 rounded-3xl flex items-center justify-center mb-8 shadow-lg">
                  <Search className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400" />
                </div>
                <div className="absolute -top-2 -right-2 animate-bounce">
                  <Sparkles className="w-6 h-6 text-yellow-500" />
                </div>
              </div>

              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">No Posts Found</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
                We couldn't find any posts in this category. Try selecting a different category or check back later for new content!
              </p>

              <button
                onClick={() => setSelectedCategory('all')}
                className="inline-flex items-center gap-2 px-4 py-2 sm:px-6 sm:py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 text-sm sm:text-base"
              >
                <Grid className="w-4 h-4 sm:w-5 sm:h-5" />
                <span className="hidden sm:inline">View All Posts</span>
                <span className="sm:hidden">All Posts</span>
              </button>
            </div>
          )}
        </div>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-3xl p-8 sm:p-12 text-center text-white mb-16">
          <div className="max-w-3xl mx-auto">
            <h3 className="text-3xl sm:text-4xl font-bold mb-6">Ready to Create Amazing AI Art?</h3>
            <p className="text-xl mb-8 opacity-90">
              Put your new knowledge to the test! Generate stunning AI images with our free, no-signup-required tool.
            </p>
            <Link
              to="/"
              className="inline-flex items-center gap-2 sm:gap-3 px-4 py-3 sm:px-8 sm:py-4 bg-white text-blue-600 font-bold rounded-xl hover:bg-gray-100 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 text-sm sm:text-base"
            >
              <Wand2 className="w-5 h-5 sm:w-6 sm:h-6" />
              <span className="hidden sm:inline">Start Creating Now</span>
              <span className="sm:hidden">Create Now</span>
              <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Blog;