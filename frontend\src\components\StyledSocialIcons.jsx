import React from 'react';

const StyledSocialIcons = ({ compact = false }) => {
  return (
    <div className={`social-card ${compact ? 'compact' : ''}`}>
      <ul className="social-list">
        <li className="iso-pro facebook">
          <span />
          <span />
          <span />
          <a href="https://facebook.com/genfreeai" target="_blank" rel="noopener noreferrer">
            <svg viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg" className="svg">
              <path d="M279.14 288l14.22-92.66h-88.91v-60.13c0-25.35 12.42-50.06 52.24-50.06h40.42V6.26S260.43 0 225.36 0c-73.22 0-121.08 44.38-121.08 124.72v70.62H22.89V288h81.39v224h100.17V288z" />
            </svg>
          </a>
          <div className="text">Facebook</div>
        </li>

        <li className="iso-pro twitter">
          <span />
          <span />
          <span />
          <a href="https://twitter.com/genfreeai" target="_blank" rel="noopener noreferrer">
            <svg className="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
            </svg>
          </a>
          <div className="text">Twitter</div>
        </li>

        <li className="iso-pro instagram">
          <span />
          <span />
          <span />
          <a href="https://instagram.com/genfreeai" target="_blank" rel="noopener noreferrer">
            <svg className="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
              <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z" />
            </svg>
          </a>
          <div className="text">Instagram</div>
        </li>

        <li className="iso-pro tiktok">
          <span />
          <span />
          <span />
          <a href="https://tiktok.com/@genfreeai" target="_blank" rel="noopener noreferrer">
            <svg className="svg" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z" />
            </svg>
          </a>
          <div className="text">TikTok</div>
        </li>

        <li className="iso-pro linkedin">
          <span />
          <span />
          <span />
          <a href="https://linkedin.com/company/genfreeai" target="_blank" rel="noopener noreferrer">
            <svg className="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
            </svg>
          </a>
          <div className="text">LinkedIn</div>
        </li>

        <li className="iso-pro youtube">
          <span />
          <span />
          <span />
          <a href="https://youtube.com/@genfreeai" target="_blank" rel="noopener noreferrer">
            <svg className="svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <path d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" />
            </svg>
          </a>
          <div className="text">YouTube</div>
        </li>
      </ul>

      <style jsx ="true">{`
        .social-card {
          max-width: fit-content;
          border-radius: 15px;
          display: flex;
          flex-direction: column;
          align-content: center;
          justify-content: center;
          gap: 1rem;
          backdrop-filter: blur(15px);
          background: rgba(255, 255, 255, 0.1);
          box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.192),
            inset 0 0 5px rgba(255, 255, 255, 0.274), 0 5px 5px rgba(0, 0, 0, 0.164);
          transition: 0.5s;
          margin: 0 auto;
        }

        .social-card:hover {
          background: rgba(173, 173, 173, 0.05);
        }

        .social-list {
          padding: 1rem;
          display: flex;
          list-style: none;
          gap: 1rem;
          align-items: center;
          justify-content: center;
          align-content: center;
          flex-wrap: wrap;
          flex-direction: row;
          margin: 0;
        }

        .social-list li {
          cursor: pointer;
          position: relative;
        }

        .svg {
          transition: all 0.3s;
          padding: 1rem;
          height: 60px;
          width: 60px;
          border-radius: 100%;
          fill: currentColor;
          box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.3),
            inset 0 0 5px rgba(255, 255, 255, 0.5), 0 5px 5px rgba(0, 0, 0, 0.164);
        }

        .text {
          opacity: 0;
          border-radius: 5px;
          padding: 5px 10px;
          transition: all 0.3s;
          background-color: rgba(255, 255, 255, 0.9);
          position: absolute;
          z-index: 9999;
          box-shadow: -5px 0 1px rgba(153, 153, 153, 0.2),
            -10px 0 1px rgba(153, 153, 153, 0.2),
            inset 0 0 20px rgba(255, 255, 255, 0.3),
            inset 0 0 5px rgba(255, 255, 255, 0.5), 0 5px 5px rgba(0, 0, 0, 0.082);
          font-size: 12px;
          font-weight: 500;
          white-space: nowrap;
          top: -40px;
          left: 50%;
          transform: translateX(-50%);
        }

        /* Isometric projection */
        .iso-pro {
          transition: 0.5s;
          position: relative;
        }

        .iso-pro:hover .svg {
          transform: translate(5px, -5px);
        }

        .iso-pro:hover .text {
          opacity: 1;
          transform: translateX(-50%) translate(5px, -5px) skew(-5deg);
        }

        .iso-pro span {
          opacity: 0;
          position: absolute;
          border-radius: 50%;
          transition: all 0.3s;
          height: 60px;
          width: 60px;
          top: 0;
          left: 0;
          box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.3),
            inset 0 0 5px rgba(255, 255, 255, 0.5), 0 5px 5px rgba(0, 0, 0, 0.164);
        }

        .iso-pro:hover span {
          opacity: 1;
        }

        .iso-pro:hover span:nth-child(1) {
          opacity: 0.2;
        }

        .iso-pro:hover span:nth-child(2) {
          opacity: 0.4;
          transform: translate(5px, -5px);
        }

        .iso-pro:hover span:nth-child(3) {
          opacity: 0.6;
          transform: translate(10px, -10px);
        }

        /* Platform-specific colors */
        .facebook .svg {
          color: #1877f2;
        }
        .facebook span {
          background-color: #1877f2;
        }
        .facebook .text {
          color: #1877f2;
        }

        .twitter .svg {
          color: #000000;
        }
        .twitter span {
          background-color: #000000;
        }
        .twitter .text {
          color: #000000;
        }

        .instagram .svg {
          color: #E4405F;
        }
        .instagram span {
          background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
        }
        .instagram .text {
          color: #E4405F;
        }

        .tiktok .svg {
          color: #000000;
        }
        .tiktok span {
          background-color: #000000;
        }
        .tiktok .text {
          color: #000000;
        }

        .linkedin .svg {
          color: #0077b5;
        }
        .linkedin span {
          background-color: #0077b5;
        }
        .linkedin .text {
          color: #0077b5;
        }

        .youtube .svg {
          color: #FF0000;
        }
        .youtube span {
          background-color: #FF0000;
        }
        .youtube .text {
          color: #FF0000;
        }

        /* Compact version for footers */
        .social-card.compact {
          background: transparent;
          box-shadow: none;
          backdrop-filter: none;
        }

        .social-card.compact .social-list {
          padding: 0;
          gap: 0.75rem;
        }

        .social-card.compact .svg {
          height: 40px;
          width: 40px;
          padding: 0.5rem;
        }

        .social-card.compact .iso-pro span {
          height: 40px;
          width: 40px;
        }

        .social-card.compact .text {
          font-size: 10px;
          top: -35px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
          .social-list {
            flex-direction: row;
            gap: 0.5rem;
          }

          .svg {
            height: 50px;
            width: 50px;
            padding: 0.75rem;
          }

          .iso-pro span {
            height: 50px;
            width: 50px;
          }

          .social-card.compact .svg {
            height: 35px;
            width: 35px;
            padding: 0.4rem;
          }

          .social-card.compact .iso-pro span {
            height: 35px;
            width: 35px;
          }
        }
      `}</style>
    </div>
  );
};

export default StyledSocialIcons;
