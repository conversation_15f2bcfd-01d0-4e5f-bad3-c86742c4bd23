import { BrowserRouter as Router, Routes, Route, useNavigate } from 'react-router-dom';
import { NotificationProvider, useNotification } from './contexts/NotificationContext';
import StyledSocialIcons from './components/StyledSocialIcons';
import Navigation from './components/Navigation';
import ScrollToTop from './components/ScrollToTop';
import Home from './pages/Home';
import History from './pages/History';
import About from './pages/About';
import FAQ from './pages/FAQ';
import Terms from './pages/Terms';
import Blog from './pages/Blog';
import AdminPanel from './pages/AdminPanel';
import ImageDownload from './pages/ImageDownload';
import HistoryNotification from './components/HistoryNotification';

const AppContent = () => {
  const navigate = useNavigate();
  const { notification, hideNotification } = useNotification();

  const handleHistoryClick = () => {
    hideNotification();
  };

  const handleTermsClick = () => {
    navigate('/terms');
  };

  return (
    <div className={`min-h-screen bg-[#111828] dark:bg-gray-900 text-white dark:text-white`}>
      {/* Scroll to top on route change */}
      <ScrollToTop />

      {/* Navigation */}
      <Navigation />

      {/* Main content */}
      <main className="relative">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/history" element={<History />} />
          <Route path="/about" element={<About />} />
          <Route path="/faq" element={<FAQ />} />
          <Route path="/terms" element={<Terms />} />
          <Route path="/blog" element={<Blog />} />
          <Route path="/admin/pass=4.67Ssc5.00" element={<AdminPanel />} />
          <Route path="/image/download" element={<ImageDownload />} />
        </Routes>
      </main>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-16 animate-fade-in-up">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center space-y-6">
            <p className="text-gray-600 dark:text-gray-300">
              Powered by <span className="bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 bg-clip-text text-transparent animate-gradient-x font-semibold">Gen Free AI</span> • Create stunning images from text descriptions
            </p>

            {/* Social Media Links */}
            <StyledSocialIcons compact={true} />

            <div className="flex justify-center space-x-6 text-sm text-gray-500 mt-2 dark:text-gray-400">
              <span>© 2025 Gen Free AI. All rights reserved.</span>
              <span>•</span>
              <span>Powered by genfreeai</span>
              <span>•</span>
              <button
                onClick={handleTermsClick}
                className="hover:text-gray-700 dark:hover:text-gray-300 transition-colors cursor-pointer"
              >
                Terms & Conditions
              </button>
            </div>
          </div>
        </div>
      </footer>

      {/* History Notification */}
      <HistoryNotification
        isVisible={notification.isVisible && notification.type === 'history'}
        onClose={hideNotification}
        onHistoryClick={handleHistoryClick}
        imageCount={notification.imageCount}
      />
    </div>
  );
};

function App() {
  return (
    <NotificationProvider>
      <Router>
        <AppContent />
      </Router>
    </NotificationProvider>
  );
}

export default App;