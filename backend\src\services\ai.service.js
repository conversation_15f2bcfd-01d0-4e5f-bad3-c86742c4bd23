const Replicate = require("replicate");

const aiservice = async(prompt, aspectRatio = '1:1')=>{
try {
    const replicate = new Replicate({
  auth: process.env.AI_KEY,
});

const output =  await replicate.run(
  "black-forest-labs/flux-schnell",
  {
    input: {
      aspect_ratio: aspectRatio,
      prompt: prompt,
      scheduler: "K_EULER",
      output_format: "jpg",
      num_outputs: 1,
      guidance_scale: 0,
      negative_prompt: "worst quality, low quality",
      num_inference_steps: 4
    }
  }
);

// To access the file URL:
const imageUrlObject = output[0].url();
const imageUrl = imageUrlObject.href; // Convert URL object to string

return imageUrl;

} catch (error) {
    console.error('❌ AI service error:', error);
}

}
module.exports = aiservice;