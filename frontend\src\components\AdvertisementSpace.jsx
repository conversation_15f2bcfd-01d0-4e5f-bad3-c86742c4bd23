import React from 'react';

const AdvertisementSpace = ({
  className = ""
}) => {
  // Display the actual advertisement
  return (
    <div className={`rounded-lg overflow-hidden ${className}`}>
      <a
        href="https://s.daraz.com.bd/s.Z656m?cc"
        target="_blank"
        rel="noopener noreferrer"
        className="block hover:opacity-90 transition-opacity duration-200"
      >
        <img
          src="/ads.png"
          alt="X15 TWS Bluetooth Wireless Earbuds - Sports Headphone Advertisement"
          className="w-full h-auto object-cover rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200"
        />
      </a>
    </div>
  );
};

// Example of how to implement real ads in the future:
//
// const AdvertisementSpace = ({ className = "" }) => {
//   return (
//     <div className={className}>
//       {/* Google AdSense Example */}
//       <ins className="adsbygoogle"
//            style={{display: 'block'}}
//            data-ad-client="ca-pub-xxxxxxxxxx"
//            data-ad-slot="xxxxxxxxxx"
//            data-ad-format="auto"
//            data-full-width-responsive="true">
//       </ins>
//
//       {/* Or Custom Ad */}
//       <div className="ad-container">
//         <img src="/path/to/ad-image.jpg" alt="Advertisement" />
//         <a href="https://advertiser-link.com" target="_blank" rel="noopener noreferrer">
//           Learn More
//         </a>
//       </div>
//     </div>
//   );
// };

export default AdvertisementSpace;
