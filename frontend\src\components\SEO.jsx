import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

const SEO = ({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  noindex = false
}) => {
  const location = useLocation();

  // Default SEO values
  const defaultTitle = "🎨 Gen Free AI - Free AI Image Generator | Create Stunning Images from Text";
  const defaultDescription = "Generate beautiful, high-quality 1024x1024 images from text descriptions using advanced AI technology. Gen Free AI offers free online AI image generation with instant results, no signup required. Best free alternative to Midjourney and DALL-E!";
  const defaultKeywords = "free AI image generator, Gen Free AI, text to image AI, AI art generator, create images from text, AI image creation, artificial intelligence art, DALL-E alternative, Midjourney free, stable diffusion online, AI design tool, generate AI images, text to image converter, AI artwork generator, free AI art maker, online image generator, AI illustration tool, digital art creator";
  const defaultImage = "https://genfreeai.com/icon.png";
  const baseUrl = "https://genfreeai.com";

  // Use provided values or defaults
  const seoTitle = title || defaultTitle;
  const seoDescription = description || defaultDescription;
  const seoKeywords = keywords || defaultKeywords;
  const seoImage = image || defaultImage;
  const seoUrl = url || `${baseUrl}${location.pathname}`;

  useEffect(() => {
    // Update document title
    document.title = seoTitle;

    // Update basic meta tags
    updateMetaTag('name', 'description', seoDescription);
    updateMetaTag('name', 'keywords', seoKeywords);
    updateMetaTag('name', 'robots', noindex ? 'noindex, nofollow' : 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1');
    updateMetaTag('name', 'googlebot', 'index, follow');
    updateMetaTag('name', 'bingbot', 'index, follow');
    updateMetaTag('name', 'author', 'Gen Free AI');
    updateMetaTag('name', 'language', 'English');
    updateMetaTag('name', 'geo.region', 'US');
    updateMetaTag('name', 'theme-color', '#3B82F6');

    // Update Open Graph tags
    updateMetaTag('property', 'og:title', seoTitle);
    updateMetaTag('property', 'og:description', seoDescription);
    updateMetaTag('property', 'og:image', seoImage);
    updateMetaTag('property', 'og:image:width', '1200');
    updateMetaTag('property', 'og:image:height', '630');
    updateMetaTag('property', 'og:image:alt', 'Gen Free AI - Free AI Image Generator');
    updateMetaTag('property', 'og:url', seoUrl);
    updateMetaTag('property', 'og:type', type);
    updateMetaTag('property', 'og:site_name', 'Gen Free AI');
    updateMetaTag('property', 'og:locale', 'en_US');

    // Update Twitter tags
    updateMetaTag('name', 'twitter:card', 'summary_large_image');
    updateMetaTag('name', 'twitter:title', seoTitle);
    updateMetaTag('name', 'twitter:description', seoDescription);
    updateMetaTag('name', 'twitter:image', seoImage);
    updateMetaTag('name', 'twitter:image:alt', 'Gen Free AI - Free AI Image Generator');
    updateMetaTag('name', 'twitter:site', '@genfreeai');
    updateMetaTag('name', 'twitter:creator', '@genfreeai');

    // Update canonical URL
    updateCanonicalUrl(seoUrl);

    // Add structured data
    addStructuredData();

  }, [seoTitle, seoDescription, seoKeywords, seoImage, seoUrl, type, noindex]);

  const updateMetaTag = (attribute, value, content) => {
    let element = document.querySelector(`meta[${attribute}="${value}"]`);
    if (element) {
      element.setAttribute('content', content);
    } else {
      element = document.createElement('meta');
      element.setAttribute(attribute, value);
      element.setAttribute('content', content);
      document.head.appendChild(element);
    }
  };

  const updateCanonicalUrl = (url) => {
    let element = document.querySelector('link[rel="canonical"]');
    if (element) {
      element.setAttribute('href', url);
    } else {
      element = document.createElement('link');
      element.setAttribute('rel', 'canonical');
      element.setAttribute('href', url);
      document.head.appendChild(element);
    }
  };

  const addStructuredData = () => {
    // Remove existing structured data
    const existingScript = document.querySelector('script[type="application/ld+json"]');
    if (existingScript) {
      existingScript.remove();
    }

    // Add comprehensive structured data
    const structuredData = {
      "@context": "https://schema.org",
      "@graph": [
        {
          "@type": "WebApplication",
          "@id": `${baseUrl}/#webapp`,
          "name": "Gen Free AI",
          "alternateName": "Gen Free AI Image Generator",
          "description": seoDescription,
          "url": baseUrl,
          "applicationCategory": "DesignApplication",
          "operatingSystem": "Web Browser",
          "browserRequirements": "Requires JavaScript. Requires HTML5.",
          "softwareVersion": "1.0",
          "datePublished": "2024-01-01",
          "dateModified": new Date().toISOString().split('T')[0],
          "inLanguage": "en-US",
          "isAccessibleForFree": true,
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock",
            "validFrom": "2024-01-01"
          },
          "creator": {
            "@type": "Organization",
            "@id": `${baseUrl}/#organization`
          },
          "featureList": [
            "Free AI Image Generation",
            "Text to Image Conversion",
            "High Quality 1024x1024 Images",
            "No Registration Required",
            "Instant Generation",
            "Commercial Use Rights"
          ],
          "screenshot": seoImage,
          "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.8",
            "ratingCount": "1250",
            "bestRating": "5",
            "worstRating": "1"
          }
        },
        {
          "@type": "Organization",
          "@id": `${baseUrl}/#organization`,
          "name": "Gen Free AI",
          "url": baseUrl,
          "logo": {
            "@type": "ImageObject",
            "url": `${baseUrl}/icon.png`,
            "width": 512,
            "height": 512
          },
          "sameAs": [
            "https://facebook.com/genfreeai",
            "https://twitter.com/genfreeai",
            "https://instagram.com/genfreeai",
            "https://linkedin.com/company/genfreeai",
            "https://tiktok.com/@genfreeai",
            "https://youtube.com/@genfreeai"
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "email": "<EMAIL>",
            "contactType": "customer service"
          }
        },
        {
          "@type": "WebSite",
          "@id": `${baseUrl}/#website`,
          "url": baseUrl,
          "name": "Gen Free AI",
          "description": seoDescription,
          "publisher": {
            "@id": `${baseUrl}/#organization`
          },
          "potentialAction": [
            {
              "@type": "SearchAction",
              "target": {
                "@type": "EntryPoint",
                "urlTemplate": `${baseUrl}/search?q={search_term_string}`
              },
              "query-input": "required name=search_term_string"
            }
          ],
          "inLanguage": "en-US"
        }
      ]
    };

    // Add FAQ structured data if on FAQ page
    if (location.pathname === '/faq') {
      structuredData["@graph"].push({
        "@type": "FAQPage",
        "mainEntity": [
          {
            "@type": "Question",
            "name": "Is Gen Free AI really free?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Yes! Gen Free AI is completely free to use. There are no hidden fees, subscriptions, or premium tiers. We believe AI creativity should be accessible to everyone."
            }
          },
          {
            "@type": "Question",
            "name": "How do I generate an image?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Simply go to our Generate page, type a detailed description of what you want to create in the prompt box, and click 'Generate Image'. Our AI will process your request and create a unique image based on your description."
            }
          },
          {
            "@type": "Question",
            "name": "What image quality do you provide?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "All generated images are provided in PNG format with a resolution of 1024x1024 pixels, ensuring high quality for both digital and print use."
            }
          },
          {
            "@type": "Question",
            "name": "Can I use generated images commercially?",
            "acceptedAnswer": {
              "@type": "Answer",
              "text": "Yes! All images generated through Gen Free AI can be used for commercial purposes. You have full rights to the images you create."
            }
          }
        ]
      });
    }

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);
  };

  return null; // This component doesn't render anything
};

// Page-specific SEO configurations
export const pageSEO = {
  home: {
    title: "Gen Free AI - Create Stunning Images from Text | Free AI Image Generator 2024",
    description: "🎨 Generate beautiful, high-quality 1024x1024 images from text descriptions using advanced AI technology. Gen Free AI offers free online AI image generation with instant results, no signup required. Create art, illustrations, logos, and designs effortlessly. Best free AI image generator 2024.",
    keywords: "Gen Free AI, AI image generator, text to image, artificial intelligence, image creation, AI art, free image generator, online tool, digital art, AI artwork, image synthesis, machine learning, creative AI, DALL-E alternative, Midjourney free, stable diffusion, AI art generator, text to art, AI design tool, free AI tools 2024, best AI image generator, AI illustration, AI logo maker, AI photo generator"
  },
  history: {
    title: "Image History - View & Download Your Generated AI Images | Gen Free AI",
    description: "📚 Browse, manage, and download your previously generated AI images with Gen Free AI. Search through your AI-created artwork collection with our convenient history feature. Access all your AI art creations in one place.",
    keywords: "Gen Free AI, AI image history, generated images, AI art gallery, image management, download AI images, AI artwork collection, AI art history, image gallery, AI creations, digital art collection, AI image library, generated art archive"
  },
  about: {
    title: "About Gen Free AI - Free AI Image Generator | Our Story & Mission 2024",
    description: "🚀 Learn about Gen Free AI, our mission to democratize AI image generation, and how we're making creative AI tools accessible to everyone worldwide. Contact <NAME_EMAIL> for support, partnerships, and collaboration opportunities.",
    keywords: "about gen free ai, ai image generator, free tools, mission, contact, <EMAIL>, AI technology, creative tools, democratize AI, image generation, AI company, artificial intelligence startup, free AI platform, AI accessibility, creative technology, AI innovation, machine learning company"
  },
  faq: {
    title: "FAQ - Frequently Asked Questions | Gen Free AI Help Center 2024",
    description: "❓ Find comprehensive answers to common questions about Gen Free AI. Learn how to generate images, download them, understand our privacy policies, troubleshoot issues, and get technical support. Complete help guide for AI image generation.",
    keywords: "Gen Free AI FAQ, frequently asked questions, AI image generator help, how to generate images, image download, privacy policy, technical support, troubleshooting, AI art help, image generation guide, AI tool tutorial, help center, user guide, AI image tips, generation problems, support documentation"
  },
  terms: {
    title: "Terms and Conditions | Gen Free AI Legal Information & Usage Rights",
    description: "📋 Read our comprehensive terms and conditions for using Gen Free AI. Learn about usage rights, content policies, commercial use permissions, and legal information for our free AI image generation service.",
    keywords: "Gen Free AI terms, terms and conditions, AI image generator terms, usage policy, legal information, AI art terms of service, commercial use rights, content policy, user agreement, legal compliance, AI tool terms, image rights, intellectual property, usage guidelines"
  }
};

export default SEO;
